#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手采集工具 - 整合版本
包含所有功能模块：主程序、数据采集、商品查询、类目解析、Cookie导出
"""

import sys
import os
import json
import requests
import time
import logging
import re
import concurrent.futures
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from threading import Lock
from urllib.parse import urlparse
import pandas as pd

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                QWidget, QPushButton, QLabel, QComboBox, QTableWidget,
                                QTableWidgetItem, QHeaderView, QFrame, QMessageBox,
                                QSplitter, QGroupBox, QGridLayout, QTabWidget, QLineEdit,
                                QProgressBar)
    from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QUrl
    from PyQt5.QtGui import QFont, QPalette, QColor
    try:
        from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEngineProfile
        from PyQt5.QtWebEngineCore import QWebEngineCookieStore
        WEBENGINE_AVAILABLE = True
    except ImportError:
        WEBENGINE_AVAILABLE = False
    PYQT_VERSION = 5
except ImportError:
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                    QWidget, QPushButton, QLabel, QComboBox, QTableWidget,
                                    QTableWidgetItem, QHeaderView, QFrame, QMessageBox,
                                    QSplitter, QGroupBox, QGridLayout, QTabWidget, QLineEdit,
                                    QProgressBar)
        from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QUrl
        from PyQt6.QtGui import QFont, QPalette, QColor
        try:
            from PyQt6.QtWebEngineWidgets import QWebEngineView
            from PyQt6.QtWebEngineCore import QWebEngineProfile, QWebEngineCookieStore
            WEBENGINE_AVAILABLE = True
        except ImportError:
            WEBENGINE_AVAILABLE = False
        PYQT_VERSION = 6
    except ImportError:
        print("错误：需要安装 PyQt5 或 PyQt6")
        sys.exit(1)


# ==================== 类目解析模块 ====================

class CategoryParser:
    """类目解析器"""

    def __init__(self):
        self.category_tree = {}
        self.category_mapping = {}  # 存储key到名称的映射

    def parse_category_file(self, file_path):
        """解析类目文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析JSON数据
            data = json.loads(content)

            # 提取类目数据
            if 'data' in data and 'sytWebItemTopRank' in data['data']:
                for item in data['data']['sytWebItemTopRank']:
                    if item.get('code') == 'itemCategory' and 'list' in item:
                        self.parse_category_list(item['list'])

            return True

        except Exception as e:
            print(f"解析类目文件失败: {e}")
            return False

    def parse_category_list(self, category_list):
        """解析类目列表"""
        for category in category_list:
            if category.get('hierarchy') == 0:  # 一级类目
                first_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': category.get('categoryPid', ''),
                    'children': {}
                }
                self.category_tree[category['label']] = first_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_second_level(category['children'], category['label'], category['key'])

    def parse_second_level(self, children, first_level, first_key):
        """解析二级类目"""
        for category in children:
            if category.get('hierarchy') == 1:  # 二级类目
                second_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': first_level,
                    'children': {}
                }
                self.category_tree[first_level]['children'][category['label']] = second_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_third_level(category['children'], first_level, category['label'], category['key'])

    def parse_third_level(self, children, first_level, second_level, second_key):
        """解析三级类目"""
        for category in children:
            if category.get('hierarchy') == 2:  # 三级类目
                third_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': second_key,
                    'children': []
                }
                self.category_tree[first_level]['children'][second_level]['children'][category['label']] = third_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_fourth_level(category['children'], first_level, second_level, category['label'], category['key'])

    def parse_fourth_level(self, children, first_level, second_level, third_level, third_key):
        """解析四级类目"""
        for category in children:
            if category.get('hierarchy') == 3:  # 四级类目
                fourth_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': third_key
                }
                self.category_tree[first_level]['children'][second_level]['children'][third_level]['children'].append(fourth_level_data)
                self.category_mapping[category['key']] = category['label']

    def save_category_data(self, output_path):
        """保存类目数据到文件"""
        try:
            # 确保data目录存在
            output_path.parent.mkdir(exist_ok=True)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.category_tree, f, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            print(f"保存类目数据失败: {e}")
            return False

    def get_category_count(self):
        """获取类目统计信息"""
        first_count = len(self.category_tree)
        second_count = sum(len(first_data['children']) for first_data in self.category_tree.values())
        third_count = sum(
            len(second_data['children'])
            for first_data in self.category_tree.values()
            for second_data in first_data['children'].values()
        )
        fourth_count = sum(
            len(third_data['children'])
            for first_data in self.category_tree.values()
            for second_data in first_data['children'].values()
            for third_data in second_data['children'].values()
        )

        return {
            'first_level': first_count,
            'second_level': second_count,
            'third_level': third_count,
            'fourth_level': fourth_count
        }


def parse_categories():
    """解析类目的主函数"""
    parser = CategoryParser()

    # 输入文件路径
    input_file = Path("类目响应数据.md")

    # 输出文件路径
    output_dir = Path("data")
    output_file = output_dir / "Category data.txt"

    if not input_file.exists():
        print(f"错误：未找到文件 {input_file}")
        return False

    print("开始解析类目数据...")

    # 解析文件
    if not parser.parse_category_file(input_file):
        print("解析失败")
        return False

    # 保存数据
    if not parser.save_category_data(output_file):
        print("保存失败")
        return False

    # 获取统计信息
    stats = parser.get_category_count()

    print(f"解析完成！")
    print(f"一级类目: {stats['first_level']} 个")
    print(f"二级类目: {stats['second_level']} 个")
    print(f"三级类目: {stats['third_level']} 个")
    print(f"四级类目: {stats['fourth_level']} 个")
    print(f"数据已保存到: {output_file}")

    return True


# ==================== 数据采集模块 ====================

class DataCollector(QThread):
    """数据采集器"""

    # 信号定义
    progress_updated = pyqtSignal(str)  # 进度更新信号
    data_received = pyqtSignal(list)    # 数据接收信号（批量）
    single_item_ready = pyqtSignal(dict)  # 单个商品数据就绪信号（逐行显示）
    error_occurred = pyqtSignal(str)    # 错误发生信号
    collection_finished = pyqtSignal()  # 采集完成信号
    status_changed = pyqtSignal(str)    # 状态变化信号

    def __init__(self):
        super().__init__()
        self.filters = {}
        self.category_data = {}
        self.is_running = False
        self.should_stop = False

        # 过滤设置
        self.filter_settings = {
            "成交指数最小值": 1500,
            "成交指数最大值": 999999,
            "渠道占比最小值": 85,
            "渠道占比最大值": 105
        }

        # API配置
        self.api_url = "https://syt.kwaixiaodian.com/rest/app/gateway/rank/list"
        self.total_index_api_url = "https://syt.kwaixiaodian.com/rest/app/gateway/rank/list"  # 总成交指数API
        self.timeout = 5
        self.max_retries = 3
        self.retry_delay = 2

        # 设置日志
        self.setup_logging()

        # 多线程配置
        self.max_workers = 3  # 最大并发线程数
        self.thread_lock = threading.Lock()  # 线程锁
        self.executor = None  # 线程池执行器

    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / f"collector_{datetime.now().strftime('%Y%m%d')}.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def set_filters(self, filters: Dict[str, str]):
        """设置筛选条件"""
        self.filters = filters.copy()
        self.logger.info(f"设置筛选条件: {self.filters}")

    def set_filter_settings(self, filter_settings: Dict[str, float]):
        """设置过滤设置"""
        self.filter_settings = filter_settings.copy()
        self.logger.info(f"设置过滤设置: {self.filter_settings}")

    def load_category_data(self):
        """加载类目数据"""
        try:
            category_file = Path("data/Category data.txt")
            if category_file.exists():
                with open(category_file, 'r', encoding='utf-8') as f:
                    self.category_data = json.load(f)
                self.logger.info("类目数据加载成功")
                return True
            else:
                self.logger.warning("类目数据文件不存在")
                return False
        except Exception as e:
            self.logger.error(f"加载类目数据失败: {e}")
            return False

    def load_cookies(self) -> Dict[str, str]:
        """加载Cookie"""
        # 如果正在停止，直接返回空字典
        if self.should_stop:
            return {}

        cookies = {}
        cookie_files = ["data/cookies.txt", "data/cookies2.txt"]

        for cookie_file in cookie_files:
            try:
                if Path(cookie_file).exists():
                    with open(cookie_file, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            # 解析Cookie字符串
                            for item in content.split(';'):
                                if '=' in item:
                                    key, value = item.strip().split('=', 1)
                                    cookies[key] = value
            except Exception as e:
                self.logger.error(f"读取Cookie文件 {cookie_file} 失败: {e}")

        if cookies:
            self.logger.info(f"成功加载 {len(cookies)} 个Cookie")
        else:
            self.logger.warning("未找到有效的Cookie")

        return cookies

    def generate_request_payload(self) -> Optional[Dict[str, Any]]:
        """生成请求载荷"""
        try:
            # 阶段一：请求准备
            self.progress_updated.emit("正在准备请求载荷...")

            # 验证筛选条件
            if not self.validate_filters():
                return None

            # 解析日期
            date_range = self.parse_date_range()
            if not date_range:
                return None

            # 构建基础载荷
            payload = {
                "module": "sytWebItemTopRank",
                "timeRange": "CUSTOMIZED_WEEK",
                "currentStartDay": date_range["current_start"],
                "currentEndDay": date_range["current_end"],
                "compareStartDay": date_range["compare_start"],
                "compareEndDay": date_range["compare_end"],
                "param": [],
                "pageNum": 1,
                "pageSize": 100
            }

            # 添加筛选参数
            self.add_filter_params(payload)

            self.logger.info(f"生成请求载荷: {json.dumps(payload, ensure_ascii=False)}")
            return payload

        except Exception as e:
            self.logger.error(f"生成请求载荷失败: {e}")
            self.error_occurred.emit(f"生成请求载荷失败: {e}")
            return None

    def validate_filters(self) -> bool:
        """验证筛选条件有效性"""
        required_filters = ["日期", "售卖渠道", "售卖形式", "品牌商品", "大牌大补"]

        for filter_name in required_filters:
            if filter_name not in self.filters or not self.filters[filter_name]:
                self.error_occurred.emit(f"缺少必要的筛选条件: {filter_name}")
                return False

        return True

    def parse_date_range(self) -> Optional[Dict[str, str]]:
        """解析日期范围"""
        try:
            date_str = self.filters.get("日期", "")

            # 处理"本周"格式
            if date_str.startswith("本周"):
                # 提取括号内的日期
                start_idx = date_str.find("(")
                end_idx = date_str.find(")")
                if start_idx != -1 and end_idx != -1:
                    date_str = date_str[start_idx+1:end_idx]

            # 解析日期范围，支持多种格式
            if " - " in date_str:
                # 格式: "YYYY-MM-DD - YYYY-MM-DD"
                parts = date_str.split(" - ")
                if len(parts) == 2:
                    current_start = parts[0].strip()
                    current_end = parts[1].strip()

                    # 计算对比周期（前一周）
                    start_date = datetime.strptime(current_start, "%Y-%m-%d")
                    compare_start_date = start_date - timedelta(days=7)
                    compare_end_date = compare_start_date + timedelta(days=6)

                    return {
                        "current_start": current_start,
                        "current_end": current_end,
                        "compare_start": compare_start_date.strftime("%Y-%m-%d"),
                        "compare_end": compare_end_date.strftime("%Y-%m-%d")
                    }
            elif "-" in date_str:
                # 格式: "YYYY-MM-DD-YYYY-MM-DD"
                parts = date_str.split("-")
                if len(parts) >= 6:
                    current_start = f"{parts[0]}-{parts[1]}-{parts[2]}"
                    current_end = f"{parts[3]}-{parts[4]}-{parts[5]}"

                    # 计算对比周期（前一周）
                    start_date = datetime.strptime(current_start, "%Y-%m-%d")
                    compare_start_date = start_date - timedelta(days=7)
                    compare_end_date = compare_start_date + timedelta(days=6)

                    return {
                        "current_start": current_start,
                        "current_end": current_end,
                        "compare_start": compare_start_date.strftime("%Y-%m-%d"),
                        "compare_end": compare_end_date.strftime("%Y-%m-%d")
                    }

            self.error_occurred.emit("日期格式无效")
            return None

        except Exception as e:
            self.logger.error(f"解析日期范围失败: {e}")
            self.error_occurred.emit(f"解析日期失败: {e}")
            return None

    def add_filter_params(self, payload: Dict[str, Any]):
        """添加筛选参数到载荷"""
        try:
            # 售卖渠道
            channel_value = self.filters.get("售卖渠道", "")
            if channel_value:
                payload["param"].append({
                    "code": "saleChannel",
                    "value": [channel_value]
                })

            # 售卖形式
            form_value = self.filters.get("售卖形式", "")
            if form_value:
                payload["param"].append({
                    "code": "saleForm",
                    "value": [form_value]
                })

            # 品牌商品
            brand_value = self.filters.get("品牌商品", "")
            if brand_value:
                payload["param"].append({
                    "code": "brandGoods",
                    "value": [brand_value]
                })

            # 大牌大补
            big_brand_value = self.filters.get("大牌大补", "")
            if big_brand_value:
                payload["param"].append({
                    "code": "bigBrandBigSubsidy",
                    "value": [big_brand_value]
                })

            # 类目筛选
            category_value = self.filters.get("类目", "")
            if category_value and category_value != "全部":
                # 查找类目对应的key
                category_key = self.find_category_key(category_value)
                if category_key:
                    payload["param"].append({
                        "code": "itemCategory",
                        "value": [category_key]
                    })

        except Exception as e:
            self.logger.error(f"添加筛选参数失败: {e}")

    def find_category_key(self, category_name: str) -> Optional[str]:
        """查找类目对应的key"""
        try:
            # 遍历类目数据查找匹配的名称
            for first_level, first_data in self.category_data.items():
                if first_level == category_name:
                    return first_data.get('key')

                for second_level, second_data in first_data.get('children', {}).items():
                    if second_level == category_name:
                        return second_data.get('key')

                    for third_level, third_data in second_data.get('children', {}).items():
                        if third_level == category_name:
                            return third_data.get('key')

                        for fourth_data in third_data.get('children', []):
                            if fourth_data.get('name') == category_name:
                                return fourth_data.get('key')

            return None

        except Exception as e:
            self.logger.error(f"查找类目key失败: {e}")
            return None

    def run(self):
        """主运行方法"""
        try:
            self.is_running = True
            self.should_stop = False

            # 阶段一：初始化
            self.status_changed.emit("正在初始化...")
            self.progress_updated.emit("开始数据采集...")

            # 加载类目数据
            if not self.load_category_data():
                self.error_occurred.emit("加载类目数据失败")
                return

            # 加载Cookie
            cookies = self.load_cookies()
            if not cookies:
                self.error_occurred.emit("未找到有效的Cookie，请先导出Cookie")
                return

            # 生成请求载荷
            payload = self.generate_request_payload()
            if not payload:
                return

            # 阶段二：数据采集
            self.status_changed.emit("正在采集数据...")
            self.collect_data_with_pagination(payload, cookies)

        except Exception as e:
            self.logger.error(f"数据采集过程中发生错误: {e}")
            self.error_occurred.emit(f"采集失败: {e}")
        finally:
            self.is_running = False
            self.collection_finished.emit()

    def collect_data_with_pagination(self, base_payload: Dict[str, Any], cookies: Dict[str, str]):
        """分页采集数据"""
        try:
            all_items = []
            page_num = 1
            total_pages = 1

            # 创建线程池
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers)

            while page_num <= total_pages and not self.should_stop:
                # 更新载荷的页码
                payload = base_payload.copy()
                payload["pageNum"] = page_num

                self.progress_updated.emit(f"正在采集第 {page_num} 页数据...")

                # 发送请求
                response_data = self.send_request(payload, cookies)
                if not response_data:
                    break

                # 解析响应
                items, pagination_info = self.parse_response(response_data)
                if not items:
                    break

                # 更新总页数
                if pagination_info:
                    total_pages = pagination_info.get('totalPages', 1)

                # 处理当前页的数据
                self.process_page_items(items, all_items)

                page_num += 1

                # 添加延迟避免请求过快
                if not self.should_stop:
                    time.sleep(0.5)

            # 关闭线程池
            if self.executor:
                self.executor.shutdown(wait=True)

            if all_items and not self.should_stop:
                self.progress_updated.emit(f"采集完成，共获取 {len(all_items)} 个商品")
                self.data_received.emit(all_items)
            elif self.should_stop:
                self.progress_updated.emit("采集已停止")
            else:
                self.error_occurred.emit("未获取到任何数据")

        except Exception as e:
            self.logger.error(f"分页采集数据失败: {e}")
            self.error_occurred.emit(f"采集失败: {e}")

    def process_page_items(self, items: List[Dict], all_items: List[Dict]):
        """处理页面商品数据"""
        try:
            # 使用线程池并发处理商品
            futures = []

            for item in items:
                if self.should_stop:
                    break

                future = self.executor.submit(self.process_single_item, item)
                futures.append(future)

            # 收集结果
            for future in as_completed(futures):
                if self.should_stop:
                    break

                try:
                    processed_item = future.result(timeout=10)
                    if processed_item:
                        with self.thread_lock:
                            all_items.append(processed_item)
                            # 发送单个商品数据信号（用于逐行显示）
                            self.single_item_ready.emit(processed_item)

                except Exception as e:
                    self.logger.error(f"处理商品数据失败: {e}")

        except Exception as e:
            self.logger.error(f"处理页面商品失败: {e}")

    def process_single_item(self, item: Dict) -> Optional[Dict]:
        """处理单个商品数据"""
        try:
            # 检查是否应该停止
            if self.should_stop:
                return None

            # 提取基础数据
            processed_item = self.extract_basic_data(item)
            if not processed_item:
                return None

            # 应用过滤条件
            if not self.apply_filters(processed_item):
                return None

            # 获取扩展数据
            self.get_extended_data(processed_item, item)

            return processed_item

        except Exception as e:
            self.logger.error(f"处理单个商品失败: {e}")
            return None

    def extract_basic_data(self, item: Dict) -> Optional[Dict]:
        """提取基础商品数据"""
        try:
            # 提取基本信息
            basic_data = {
                "商品ID": item.get("itemId", ""),
                "商品标题": item.get("itemName", ""),
                "商品链接": f"https://www.kwaixiaodian.com/merchant/shop/detail?id={item.get('itemId', '')}",
                "成交指数": item.get("dealIndex", 0),
                "渠道占比": round(item.get("channelRatio", 0) * 100, 2) if item.get("channelRatio") else 0,
                "价格": item.get("price", 0),
                "类目": item.get("categoryName", ""),
                "品牌": item.get("brandName", ""),
                "店铺名称": item.get("shopName", ""),
                "店铺ID": item.get("shopId", ""),
                "总成交指数": 0,  # 稍后获取
                "成交指数占比": 0,  # 稍后计算
            }

            return basic_data

        except Exception as e:
            self.logger.error(f"提取基础数据失败: {e}")
            return None

    def apply_filters(self, item: Dict) -> bool:
        """应用过滤条件"""
        try:
            # 成交指数过滤
            deal_index = item.get("成交指数", 0)
            if (deal_index < self.filter_settings.get("成交指数最小值", 0) or
                deal_index > self.filter_settings.get("成交指数最大值", 999999)):
                return False

            # 渠道占比过滤
            channel_ratio = item.get("渠道占比", 0)
            if (channel_ratio < self.filter_settings.get("渠道占比最小值", 0) or
                channel_ratio > self.filter_settings.get("渠道占比最大值", 100)):
                return False

            return True

        except Exception as e:
            self.logger.error(f"应用过滤条件失败: {e}")
            return False

    def get_extended_data(self, processed_item: Dict, original_item: Dict):
        """获取扩展数据（总成交指数等）"""
        try:
            # 这里可以添加获取总成交指数的逻辑
            # 由于API限制，暂时使用默认值
            processed_item["总成交指数"] = processed_item.get("成交指数", 0) * 2  # 示例计算

            # 计算成交指数占比
            total_index = processed_item.get("总成交指数", 0)
            deal_index = processed_item.get("成交指数", 0)
            if total_index > 0:
                processed_item["成交指数占比"] = round((deal_index / total_index) * 100, 2)
            else:
                processed_item["成交指数占比"] = 0

        except Exception as e:
            self.logger.error(f"获取扩展数据失败: {e}")

    def send_request(self, payload: Dict[str, Any], cookies: Dict[str, str]) -> Optional[Dict]:
        """发送HTTP请求"""
        headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Referer": "https://syt.kwaixiaodian.com/zones/home"
        }

        # 转换Cookie字典为字符串
        cookie_string = "; ".join([f"{k}={v}" for k, v in cookies.items()])
        headers["Cookie"] = cookie_string

        for attempt in range(self.max_retries):
            if self.should_stop:
                return None

            try:
                response = requests.post(
                    self.api_url,
                    json=payload,
                    headers=headers,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    self.logger.warning(f"请求失败，状态码: {response.status_code}")

            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")

                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)

        return None

    def parse_response(self, response_data: Dict) -> tuple:
        """解析响应数据"""
        try:
            if response_data.get("result") != 1:
                error_msg = response_data.get("error_msg", "未知错误")
                self.logger.error(f"API返回错误: {error_msg}")
                return [], None

            data = response_data.get("data", {})
            items = []
            pagination_info = None

            # 查找商品数据
            syt_web_item_top_rank = data.get("sytWebItemTopRank", [])
            for rank_item in syt_web_item_top_rank:
                if rank_item.get("code") == "itemRank":
                    items = rank_item.get("list", [])
                    pagination_info = rank_item.get("pagination", {})
                    break

            return items, pagination_info

        except Exception as e:
            self.logger.error(f"解析响应数据失败: {e}")
            return [], None

    def stop(self):
        """停止采集"""
        self.should_stop = True
        if self.executor:
            self.executor.shutdown(wait=False)
        self.logger.info("数据采集已停止")


# ==================== 商品查询模块 ====================

class ProductQueryWorker(QThread):
    """商品查询工作线程"""

    # 信号定义
    progress_updated = pyqtSignal(str)  # 进度更新
    title_received = pyqtSignal(int, str, str)  # 标题接收 (行号, 标题, 链接)
    sales_data_received = pyqtSignal(int, dict)  # 销量数据接收 (行号, 数据字典)
    query_finished = pyqtSignal()  # 查询完成
    error_occurred = pyqtSignal(str)  # 错误发生

    def __init__(self):
        super().__init__()
        self.product_ids = []
        self.date_range = ""
        self.speed_mode = "normal"
        self.should_stop = False
        self.query_mode = "normal"  # normal 或 batch

        # 速度模式配置
        self.speed_configs = {
            "ultra_fast": {"max_workers": 20, "delay": 0.1},
            "fast": {"max_workers": 10, "delay": 0.2},
            "normal": {"max_workers": 5, "delay": 0.5},
            "slow": {"max_workers": 2, "delay": 1.0}
        }

        # API配置
        self.api_url = "https://syt.kwaixiaodian.com/rest/app/gateway/rank/item/detail"
        self.timeout = 10
        self.max_retries = 3

        # 设置日志
        self.setup_logging()

    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        self.logger = logging.getLogger(f"{__name__}_query")
        if not self.logger.handlers:
            handler = logging.FileHandler(
                log_dir / f"query_{datetime.now().strftime('%Y%m%d')}.log",
                encoding='utf-8'
            )
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    def set_query_params(self, product_ids: List[str], date_range: str, speed_mode: str = "normal", query_mode: str = "normal"):
        """设置查询参数"""
        self.product_ids = product_ids.copy()
        self.date_range = date_range
        self.speed_mode = speed_mode
        self.query_mode = query_mode
        self.should_stop = False

        self.logger.info(f"设置查询参数: {len(product_ids)} 个商品, 日期: {date_range}, 速度: {speed_mode}, 模式: {query_mode}")

    def run(self):
        """运行查询"""
        try:
            if not self.product_ids:
                self.error_occurred.emit("没有要查询的商品ID")
                return

            self.progress_updated.emit(f"开始查询 {len(self.product_ids)} 个商品...")

            # 加载Cookie
            cookies = self.load_cookies()
            if not cookies:
                self.error_occurred.emit("未找到有效的Cookie")
                return

            # 解析日期范围
            date_params = self.parse_date_range()
            if not date_params:
                self.error_occurred.emit("日期格式无效")
                return

            # 根据查询模式选择不同的查询方法
            if self.query_mode == "batch":
                self.batch_query(cookies, date_params)
            else:
                self.normal_query(cookies, date_params)

        except Exception as e:
            self.logger.error(f"查询过程中发生错误: {e}")
            self.error_occurred.emit(f"查询失败: {e}")
        finally:
            self.query_finished.emit()

    def normal_query(self, cookies: Dict[str, str], date_params: Dict[str, str]):
        """普通查询模式"""
        config = self.speed_configs.get(self.speed_mode, self.speed_configs["normal"])

        with ThreadPoolExecutor(max_workers=config["max_workers"]) as executor:
            futures = {}

            for i, product_id in enumerate(self.product_ids):
                if self.should_stop:
                    break

                future = executor.submit(self.query_single_product, product_id, cookies, date_params, i)
                futures[future] = i

                # 添加延迟
                time.sleep(config["delay"])

            # 收集结果
            for future in as_completed(futures):
                if self.should_stop:
                    break

                row = futures[future]
                try:
                    result = future.result(timeout=30)
                    if result:
                        title, link, sales_data = result
                        self.title_received.emit(row, title, link)
                        self.sales_data_received.emit(row, sales_data)

                except Exception as e:
                    self.logger.error(f"查询商品 {self.product_ids[row]} 失败: {e}")

    def batch_query(self, cookies: Dict[str, str], date_params: Dict[str, str]):
        """批量查询模式"""
        # 批量查询的实现（如果API支持）
        # 这里暂时使用普通查询
        self.normal_query(cookies, date_params)

    def query_single_product(self, product_id: str, cookies: Dict[str, str], date_params: Dict[str, str], row: int) -> Optional[tuple]:
        """查询单个商品"""
        try:
            # 构建请求载荷
            payload = {
                "itemId": product_id,
                "timeRange": "CUSTOMIZED_WEEK",
                "currentStartDay": date_params["current_start"],
                "currentEndDay": date_params["current_end"],
                "compareStartDay": date_params["compare_start"],
                "compareEndDay": date_params["compare_end"]
            }

            # 发送请求
            response_data = self.send_request(payload, cookies)
            if not response_data:
                return None

            # 解析响应
            return self.parse_product_response(response_data, product_id)

        except Exception as e:
            self.logger.error(f"查询商品 {product_id} 失败: {e}")
            return None

    def parse_product_response(self, response_data: Dict, product_id: str) -> Optional[tuple]:
        """解析商品响应数据"""
        try:
            if response_data.get("result") != 1:
                return None

            data = response_data.get("data", {})

            # 提取商品信息
            title = data.get("itemName", "")
            link = f"https://www.kwaixiaodian.com/merchant/shop/detail?id={product_id}"

            # 提取销量数据
            sales_data = {}

            # 查找销量相关数据
            for key, value in data.items():
                if "sales" in key.lower() or "deal" in key.lower() or "volume" in key.lower():
                    sales_data[key] = value

            # 如果没有找到销量数据，使用默认结构
            if not sales_data:
                sales_data = {
                    "当前周期销量": data.get("currentSales", 0),
                    "对比周期销量": data.get("compareSales", 0),
                    "销量变化": data.get("salesChange", 0),
                    "销量变化率": data.get("salesChangeRate", 0)
                }

            return title, link, sales_data

        except Exception as e:
            self.logger.error(f"解析商品响应失败: {e}")
            return None

    def load_cookies(self) -> Dict[str, str]:
        """加载Cookie"""
        cookies = {}
        cookie_files = ["data/cookies.txt", "data/cookies2.txt"]

        for cookie_file in cookie_files:
            try:
                if Path(cookie_file).exists():
                    with open(cookie_file, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            for item in content.split(';'):
                                if '=' in item:
                                    key, value = item.strip().split('=', 1)
                                    cookies[key] = value
            except Exception as e:
                self.logger.error(f"读取Cookie文件 {cookie_file} 失败: {e}")

        return cookies

    def parse_date_range(self) -> Optional[Dict[str, str]]:
        """解析日期范围"""
        try:
            date_str = self.date_range

            # 处理不同的日期格式
            if " - " in date_str:
                parts = date_str.split(" - ")
                if len(parts) == 2:
                    current_start = parts[0].strip()
                    current_end = parts[1].strip()

                    # 计算对比周期
                    start_date = datetime.strptime(current_start, "%Y-%m-%d")
                    compare_start_date = start_date - timedelta(days=7)
                    compare_end_date = compare_start_date + timedelta(days=6)

                    return {
                        "current_start": current_start,
                        "current_end": current_end,
                        "compare_start": compare_start_date.strftime("%Y-%m-%d"),
                        "compare_end": compare_end_date.strftime("%Y-%m-%d")
                    }

            return None

        except Exception as e:
            self.logger.error(f"解析日期范围失败: {e}")
            return None

    def send_request(self, payload: Dict[str, Any], cookies: Dict[str, str]) -> Optional[Dict]:
        """发送HTTP请求"""
        headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Referer": "https://syt.kwaixiaodian.com/zones/home"
        }

        cookie_string = "; ".join([f"{k}={v}" for k, v in cookies.items()])
        headers["Cookie"] = cookie_string

        for attempt in range(self.max_retries):
            if self.should_stop:
                return None

            try:
                response = requests.post(
                    self.api_url,
                    json=payload,
                    headers=headers,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()

            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)

        return None

    def stop(self):
        """停止查询"""
        self.should_stop = True


# ==================== Cookie导出模块 ====================

if WEBENGINE_AVAILABLE:
    class CookieManager:
        """Cookie管理器"""

        def __init__(self, cookie_store):
            self.cookie_store = cookie_store
            self.cookies = []
            self.cookie_store.cookieAdded.connect(self.on_cookie_added)
            self.cookie_store.cookieRemoved.connect(self.on_cookie_removed)

        def on_cookie_added(self, cookie):
            """Cookie添加时的回调"""
            cookie_data = {
                'name': cookie.name().data().decode('utf-8'),
                'value': cookie.value().data().decode('utf-8'),
                'domain': cookie.domain(),
                'path': cookie.path(),
                'secure': cookie.isSecure(),
                'httpOnly': cookie.isHttpOnly()
            }

            # 避免重复添加
            existing_cookie = next((c for c in self.cookies
                                  if c['name'] == cookie_data['name'] and
                                     c['domain'] == cookie_data['domain']), None)
            if existing_cookie:
                self.cookies.remove(existing_cookie)

            self.cookies.append(cookie_data)

        def on_cookie_removed(self, cookie):
            """Cookie移除时的回调"""
            cookie_name = cookie.name().data().decode('utf-8')
            cookie_domain = cookie.domain()
            self.cookies = [c for c in self.cookies
                           if not (c['name'] == cookie_name and c['domain'] == cookie_domain)]

        def get_cookies_for_domain(self, url):
            """获取指定域名的Cookie"""
            parsed_url = urlparse(url)
            hostname = parsed_url.hostname
            if not hostname:
                return []

            # 获取主域名
            domain_parts = hostname.split('.')
            if len(domain_parts) >= 2:
                main_domain = '.'.join(domain_parts[-2:])
            else:
                main_domain = hostname

            # 过滤相关域名的Cookie
            relevant_cookies = []
            for cookie in self.cookies:
                cookie_domain = cookie['domain']
                if (main_domain in cookie_domain or
                    cookie_domain == '.' + main_domain or
                    cookie_domain == main_domain or
                    hostname in cookie_domain or
                    cookie_domain.endswith('.' + main_domain)):
                    relevant_cookies.append(cookie)

            return relevant_cookies

    if WEBENGINE_AVAILABLE:
        class CookieExportWorker(QThread):
            """Cookie导出工作线程"""
            finished = pyqtSignal(str, int)  # 结果消息, Cookie数量
            error = pyqtSignal(str)  # 错误消息
            progress = pyqtSignal(str)  # 进度消息
            url_changed = pyqtSignal(str)  # URL变化信号

            def __init__(self, cookie_manager, browser_page, browser_view):
                super().__init__()
                self.cookie_manager = cookie_manager
                self.browser_page = browser_page
                self.browser_view = browser_view
                self.js_cookies = []

                # 定义要采集的网页
                self.urls = [
                    {
                        'url': 'https://syt.kwaixiaodian.com/zones/home',
                        'filename': 'cookies.txt',
                        'name': '网页1'
                    },
                    {
                        'url': 'https://cps.kwaixiaodian.com/pc/promoter/selection-center/home',
                        'filename': 'cookies2.txt',
                        'name': '网页2'
                    }
                ]
                self.current_index = 0

            def run(self):
                try:
                    self.progress.emit("开始采集Cookie...")
                    self.process_next_url()

                except Exception as e:
                    self.error.emit(f"导出 Cookie 时出错：{str(e)}")

            def process_next_url(self):
                """处理下一个URL"""
                if self.current_index < len(self.urls):
                    url_info = self.urls[self.current_index]
                    url = url_info['url']
                    name = url_info['name']

                    self.progress.emit(f"正在访问{name}: {url}")

                    # 发送URL变化信号，让主线程加载页面
                    self.url_changed.emit(url)

                    # 等待页面加载
                    self.msleep(3000)  # 等待3秒让页面完全加载

                    # 获取并保存当前页面的Cookie
                    self.export_current_cookies()

                else:
                    # 所有URL处理完成
                    self.finished.emit("所有网页Cookie采集完成！", 0)

            def export_current_cookies(self):
                """导出当前页面的Cookie"""
                try:
                    url_info = self.urls[self.current_index]
                    url = url_info['url']
                    filename = url_info['filename']
                    name = url_info['name']

                    self.progress.emit(f"正在获取{name}的Cookie...")

                    # 等待JavaScript Cookie获取完成
                    self.msleep(1000)

                    # 从Cookie管理器获取Cookie
                    store_cookies = self.cookie_manager.get_cookies_for_domain(url)

                    # 合并JavaScript获取的Cookie和存储的Cookie
                    all_cookies = []

                    # 添加JavaScript获取的Cookie
                    for js_cookie in self.js_cookies:
                        if js_cookie.get('name') and js_cookie.get('value'):
                            all_cookies.append(js_cookie)

                    # 添加存储的Cookie（避免重复）
                    for store_cookie in store_cookies:
                        existing = next((c for c in all_cookies
                                       if c.get('name') == store_cookie['name']), None)
                        if not existing:
                            all_cookies.append(store_cookie)

                    if not all_cookies:
                        self.progress.emit(f"⚠️ {name}未找到Cookie，继续下一个...")
                    else:
                        # 格式化Cookie字符串
                        cookie_string = '; '.join([f"{cookie['name']}={cookie['value']}"
                                                 for cookie in all_cookies
                                                 if cookie.get('name') and cookie.get('value')])

                        if cookie_string:
                            # 创建data目录
                            data_dir = Path("data")
                            data_dir.mkdir(exist_ok=True)

                            # 保存Cookie到指定文件
                            cookie_file = data_dir / filename
                            with open(cookie_file, 'w', encoding='utf-8') as f:
                                f.write(cookie_string)

                            self.progress.emit(f"✅ {name}成功保存 {len(all_cookies)} 个Cookie到 {filename}")
                        else:
                            self.progress.emit(f"⚠️ {name}未找到有效Cookie，继续下一个...")

                    # 处理下一个URL
                    self.current_index += 1
                    self.msleep(1000)  # 等待1秒
                    self.process_next_url()

                except Exception as e:
                    self.error.emit(f"处理{name}时出错：{str(e)}")

            def set_js_cookies(self, cookies):
                """设置JavaScript获取的Cookie"""
                self.js_cookies = cookies if cookies else []

        class CookieExporterMainWindow(QMainWindow):
            """Cookie导出工具主窗口"""

            def __init__(self):
                super().__init__()
                self.init_ui()
                self.setup_browser()

            def init_ui(self):
                """初始化用户界面"""
                self.setWindowTitle("Cookies获取")
                self.setGeometry(100, 100, 1200, 800)

                # 创建中央窗口部件
                central_widget = QWidget()
                self.setCentralWidget(central_widget)

                # 创建主布局
                main_layout = QVBoxLayout(central_widget)

                # 创建工具栏布局
                toolbar_layout = QHBoxLayout()

                # 导出Cookie按钮
                self.export_button = QPushButton("一键导出 Cookie")
                self.export_button.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                """)
                self.export_button.clicked.connect(self.export_cookies)

                # 状态标签
                self.status_label = QLabel("就绪")
                self.status_label.setStyleSheet("""
                    QLabel {
                        padding: 8px;
                        border-radius: 4px;
                        background-color: #d1ecf1;
                        color: #0c5460;
                        border: 1px solid #bee5eb;
                        font-weight: bold;
                    }
                """)

                # 进度条
                self.progress_bar = QProgressBar()
                self.progress_bar.setVisible(False)

                toolbar_layout.addWidget(self.status_label)
                toolbar_layout.addStretch()
                toolbar_layout.addWidget(self.export_button)

                main_layout.addLayout(toolbar_layout)
                main_layout.addWidget(self.progress_bar)

                # 创建浏览器视图
                self.browser = QWebEngineView()
                main_layout.addWidget(self.browser)


# ==================== 主窗口类 ====================

class NoAnimationComboBox(QComboBox):
    """无动画下拉框 - 简化版本"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置基本样式
        self.setStyleSheet("""
            QComboBox {
                border: 1px solid #cccccc;
                border-radius: 3px;
                padding: 5px;
                background-color: white;
                min-width: 100px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #cccccc;
                background-color: white;
                selection-background-color: #e3f2fd;
                outline: none;
                max-height: 300px;
            }
            QComboBox QAbstractItemView::item {
                height: 25px;
                padding: 2px 8px;
                border: none;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #e3f2fd !important;
                color: #1976d2 !important;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #f5f5f5 !important;
                color: #333333 !important;
            }
            QComboBox QAbstractItemView::item:focus {
                background-color: #e3f2fd !important;
                color: #1976d2 !important;
            }
        """)

        # 设置最大可见项目数，这样可以减少动画效果
        self.setMaxVisibleItems(20)

        # 设置文本省略号模式为结尾省略
        view = self.view()
        if view and hasattr(view, 'setTextElideMode'):
            view.setTextElideMode(Qt.ElideRight)  # 在右侧（结尾）显示省略号


class KuaishouCollectorMainWindow(QMainWindow):
    """快手采集工具主窗口"""

    def __init__(self):
        super().__init__()
        self.category_data = {}  # 存储类目数据
        self.data_collector = None  # 数据采集器
        self.collected_data = []  # 存储采集到的数据
        self.current_row_count = 0  # 用于跟踪当前行数，确保排名连续

        # 商品查询相关
        self.product_query_worker = None  # 商品查询工作线程
        self.query_data = []  # 存储查询到的数据

        # 数据过滤设置
        self.filter_settings = {
            "成交指数最小值": 1500,
            "成交指数最大值": 999999,
            "渠道占比最小值": 85,
            "渠道占比最大值": 105
        }

        # 设置全局字体大小（调大10%）
        self.setup_global_font()

        self.init_ui()
        self.setup_styles()
        self.load_category_data()
        self.load_filter_settings()  # 加载过滤设置
        self.check_cookies()
        self.init_data_collector()
        self.init_product_query()  # 初始化商品查询功能

    def setup_global_font(self):
        """设置全局字体大小（调大10%）"""
        try:
            # 获取当前应用程序的字体
            app = QApplication.instance()
            current_font = app.font()

            # 获取当前字体大小并调大10%
            current_size = current_font.pointSize()
            if current_size <= 0:  # 如果pointSize无效，使用pixelSize
                current_size = current_font.pixelSize()
                new_size = int(current_size * 1.1)
                current_font.setPixelSize(new_size)
            else:
                new_size = int(current_size * 1.1)
                current_font.setPointSize(new_size)

            # 应用新字体到整个应用程序
            app.setFont(current_font)

        except Exception as e:
            print(f"设置全局字体失败: {e}")

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("快手采集工具")
        self.setGeometry(100, 100, 1400, 900)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建标签页组件
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # 创建"商品采集"标签页
        self.create_collection_tab()

        # 创建"查询商品成交量"标签页
        self.create_query_tab()

    def create_collection_tab(self):
        """创建商品采集标签页"""
        # 创建标签页内容widget
        collection_widget = QWidget()

        # 创建布局
        layout = QVBoxLayout(collection_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)

        # 上半部分：筛选条件设置区域 (15%)
        filter_widget = self.create_filter_widget()
        splitter.addWidget(filter_widget)

        # 下半部分：数据展示区域 (85%)
        data_widget = self.create_data_widget()
        splitter.addWidget(data_widget)

        # 设置分割器比例 - 为数据过滤设置区域分配更多空间
        splitter.setSizes([180, 720])  # 20% : 80%
        splitter.setChildrenCollapsible(False)

        # 添加到标签页
        self.tab_widget.addTab(collection_widget, "商品采集")

    def create_query_tab(self):
        """创建查询商品成交量标签页"""
        # 创建标签页内容widget
        query_widget = QWidget()

        # 创建布局
        layout = QVBoxLayout(query_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建控制区域（占界面高度10%）
        control_widget = self.create_query_control_widget()
        layout.addWidget(control_widget)

        # 创建数据展示区域（占界面高度90%）
        data_widget = self.create_query_data_widget()
        layout.addWidget(data_widget)

        # 设置布局比例
        layout.setStretchFactor(control_widget, 1)  # 10%
        layout.setStretchFactor(data_widget, 9)     # 90%

        # 添加到标签页
        self.tab_widget.addTab(query_widget, "查询商品成交量")

    def create_query_control_widget(self):
        """创建查询控制区域"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.Box)
        control_frame.setLineWidth(1)
        control_frame.setFixedHeight(80)  # 固定高度

        layout = QHBoxLayout(control_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)

        # 时间范围选择器
        time_label = QLabel("时间范围：")
        time_label.setStyleSheet("font-size: 14px; font-weight: bold;")

        self.query_time_combo = QComboBox()
        self.query_time_combo.addItems(["30天", "60天", "90天"])
        self.query_time_combo.setCurrentText("30天")  # 默认选择30天
        self.query_time_combo.setFixedSize(100, 35)

        # 查询速度选择器
        speed_label = QLabel("查询速度：")
        speed_label.setStyleSheet("font-size: 14px; font-weight: bold;")

        self.query_speed_combo = QComboBox()
        self.query_speed_combo.addItems(["超快", "快速", "普通", "慢速"])
        self.query_speed_combo.setCurrentText("快速")  # 默认选择快速
        self.query_speed_combo.setFixedSize(100, 35)
        self.query_speed_combo.setToolTip("超快：极限速度，12个并发，延迟1-5毫秒\n快速：高速查询，8个并发，延迟5-20毫秒\n普通：平衡速度和稳定性，5个并发\n慢速：最稳定，适合网络不好时")

        # 功能按钮
        self.import_links_btn = QPushButton("导入链接")
        self.import_links_btn.setFixedSize(100, 35)
        self.import_links_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)

        self.start_query_btn = QPushButton("开始查询")
        self.start_query_btn.setFixedSize(100, 35)
        self.start_query_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        self.stop_query_btn = QPushButton("停止查询")
        self.stop_query_btn.setFixedSize(100, 35)
        self.stop_query_btn.setEnabled(False)
        self.stop_query_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        self.export_query_btn = QPushButton("导出数据")
        self.export_query_btn.setFixedSize(100, 35)
        self.export_query_btn.setEnabled(False)
        self.export_query_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        # 状态显示
        self.query_status_label = QLabel("就绪")
        self.query_status_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }
        """)

        # 添加到布局
        layout.addWidget(time_label)
        layout.addWidget(self.query_time_combo)
        layout.addWidget(speed_label)
        layout.addWidget(self.query_speed_combo)
        layout.addWidget(self.import_links_btn)
        layout.addWidget(self.start_query_btn)
        layout.addWidget(self.stop_query_btn)
        layout.addWidget(self.export_query_btn)
        layout.addStretch()
        layout.addWidget(self.query_status_label)

        # 连接按钮事件
        self.import_links_btn.clicked.connect(self.on_import_links_clicked)
        self.start_query_btn.clicked.connect(self.on_start_query_clicked)
        self.stop_query_btn.clicked.connect(self.on_stop_query_clicked)
        self.export_query_btn.clicked.connect(self.on_export_query_clicked)

        # 连接时间范围变化事件
        self.query_time_combo.currentTextChanged.connect(self.on_query_time_changed)

        return control_frame

    def create_query_data_widget(self):
        """创建查询数据展示区域"""
        data_frame = QFrame()
        data_frame.setFrameStyle(QFrame.Box)
        data_frame.setLineWidth(1)

        layout = QVBoxLayout(data_frame)
        layout.setContentsMargins(15, 15, 15, 15)

        # 创建查询数据表格
        self.create_query_data_table()
        layout.addWidget(self.query_data_table)

        return data_frame

    def create_query_data_table(self):
        """创建查询数据表格"""
        self.query_data_table = QTableWidget()

        # 初始化表格结构
        self.update_query_table_structure()

        # 设置表格样式
        self.query_data_table.horizontalHeader().setStretchLastSection(False)
        self.query_data_table.setAlternatingRowColors(True)
        self.query_data_table.setSelectionBehavior(QTableWidget.SelectRows)

        # 隐藏默认的行号
        self.query_data_table.verticalHeader().setVisible(False)

        # 设置行高
        self.query_data_table.verticalHeader().setDefaultSectionSize(40)

        # 设置列宽
        self.apply_query_table_column_widths()

    def update_query_table_structure(self):
        """更新查询表格结构"""
        # 获取选择的天数
        days = int(self.query_time_combo.currentText().replace("天", ""))

        # 生成表头
        headers = ["商品标题", "商品链接"]

        # 生成日期列（从昨天开始向前推算）
        today = datetime.now()
        start_date = today - timedelta(days=1)  # 从昨天开始

        for i in range(days):
            date = start_date - timedelta(days=i)
            date_str = date.strftime("%m/%d")
            headers.append(date_str)

        # 设置表头
        self.query_data_table.setColumnCount(len(headers))
        self.query_data_table.setHorizontalHeaderLabels(headers)

        # 应用列宽
        self.apply_query_table_column_widths()

    def apply_query_table_column_widths(self):
        """应用查询表格列宽设置"""
        try:
            header = self.query_data_table.horizontalHeader()
            column_count = self.query_data_table.columnCount()

            for i in range(column_count):
                if i == 0:  # 商品标题列 - 固定宽度300px
                    header.setSectionResizeMode(i, QHeaderView.Fixed)
                    self.query_data_table.setColumnWidth(i, 300)
                elif i == 1:  # 商品链接列 - 固定宽度150px
                    header.setSectionResizeMode(i, QHeaderView.Fixed)
                    self.query_data_table.setColumnWidth(i, 150)
                else:  # 日期列 - 固定宽度80px
                    header.setSectionResizeMode(i, QHeaderView.Fixed)
                    self.query_data_table.setColumnWidth(i, 80)

        except Exception as e:
            print(f"应用查询表格列宽设置失败: {e}")

    def on_import_links_clicked(self):
        """导入链接按钮点击事件"""
        try:
            # 获取剪切板内容
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard_text = clipboard.text()

            if not clipboard_text.strip():
                QMessageBox.warning(self, "警告", "剪切板为空，请先复制商品链接")
                return

            # 解析链接（按行分割）
            links = []
            for line in clipboard_text.strip().split('\n'):
                line = line.strip()
                if line and 'kwaixiaodian.com' in line:
                    links.append(line)

            if not links:
                QMessageBox.warning(self, "警告", "剪切板中没有找到有效的快手商品链接")
                return

            # 更新表格结构
            self.update_query_table_structure()

            # 清空现有数据
            self.query_data_table.setRowCount(0)

            # 添加链接到表格
            for i, link in enumerate(links):
                self.query_data_table.insertRow(i)

                # 商品标题列（暂时为空）
                title_item = QTableWidgetItem("")
                title_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.query_data_table.setItem(i, 0, title_item)

                # 商品链接列
                link_item = QTableWidgetItem(link)
                link_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                link_item.setToolTip(link)
                self.query_data_table.setItem(i, 1, link_item)

                # 日期列（暂时为空）
                for j in range(2, self.query_data_table.columnCount()):
                    date_item = QTableWidgetItem("")
                    date_item.setTextAlignment(Qt.AlignCenter)
                    self.query_data_table.setItem(i, j, date_item)

            # 启用开始查询按钮
            self.start_query_btn.setEnabled(True)

            self.query_status_label.setText(f"已导入 {len(links)} 个商品链接")
            self.query_status_label.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入链接失败：{str(e)}")

    def on_query_time_changed(self, text):
        """时间范围变化处理"""
        try:
            # 如果表格中有数据，询问是否要重新构建表格
            if self.query_data_table.rowCount() > 0:
                reply = QMessageBox.question(
                    self,
                    "确认",
                    "更改时间范围将重新构建表格，是否继续？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.No:
                    # 恢复原来的选择
                    self.query_time_combo.blockSignals(True)
                    # 这里需要记住之前的选择，暂时不处理
                    self.query_time_combo.blockSignals(False)
                    return

            # 更新表格结构
            self.update_query_table_structure()

        except Exception as e:
            print(f"时间范围变化处理失败: {e}")

    def create_filter_widget(self):
        """创建筛选条件设置区域"""
        filter_frame = QFrame()
        filter_frame.setFrameStyle(QFrame.Box)
        filter_frame.setLineWidth(1)

        layout = QVBoxLayout(filter_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 筛选条件区域
        filter_group = QGroupBox("条件筛选设置")
        filter_layout = QGridLayout(filter_group)
        filter_layout.setSpacing(10)

        # 创建9个筛选器
        self.create_filters(filter_layout)

        layout.addWidget(filter_group)

        # 数据过滤设置区域
        data_filter_widget = self.create_data_filter_widget()
        layout.addWidget(data_filter_widget)

        return filter_frame

    def create_data_filter_widget(self):
        """创建数据过滤设置区域"""
        # 创建数据过滤设置组
        data_filter_group = QGroupBox("数据过滤设置")
        data_filter_group.setFixedHeight(100)  # 调整高度为100px以确保按钮完全显示

        # 主水平布局：成交指数区间、渠道占比区间和功能按钮在同一水平线
        main_layout = QHBoxLayout(data_filter_group)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(30)  # 各部分之间的间距
        main_layout.setAlignment(Qt.AlignLeft)  # 整体居左对齐

        # 第一部分：成交指数区间
        trade_index_widget = QWidget()
        trade_index_widget.setFixedSize(320, 40)  # 设置固定尺寸，防止窗口放大时改变
        trade_index_layout = QHBoxLayout(trade_index_widget)
        trade_index_layout.setContentsMargins(0, 0, 0, 0)
        trade_index_layout.setSpacing(3)  # 输入框间距为3px

        trade_index_label = QLabel("成交指数区间：")
        trade_index_label.setStyleSheet("font-size: 14px; font-weight: bold;")

        self.trade_index_min_input = QLineEdit()
        self.trade_index_min_input.setPlaceholderText("最小值")
        self.trade_index_min_input.setFixedSize(85, 40)
        self.trade_index_min_input.setText(str(self.filter_settings["成交指数最小值"]))

        trade_index_separator = QLabel("~")
        trade_index_separator.setAlignment(Qt.AlignCenter)
        trade_index_separator.setFixedWidth(20)
        trade_index_separator.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            border: none;
            background: transparent;
            color: #666666;
        """)

        self.trade_index_max_input = QLineEdit()
        self.trade_index_max_input.setPlaceholderText("最大值")
        self.trade_index_max_input.setFixedSize(85, 40)
        self.trade_index_max_input.setText(str(self.filter_settings["成交指数最大值"]))

        trade_index_layout.addWidget(trade_index_label)
        trade_index_layout.addWidget(self.trade_index_min_input)
        trade_index_layout.addWidget(trade_index_separator)
        trade_index_layout.addWidget(self.trade_index_max_input)

        # 第二部分：渠道占比区间
        channel_ratio_widget = QWidget()
        channel_ratio_widget.setFixedSize(320, 40)  # 设置固定尺寸，防止窗口放大时改变
        channel_ratio_layout = QHBoxLayout(channel_ratio_widget)
        channel_ratio_layout.setContentsMargins(0, 0, 0, 0)
        channel_ratio_layout.setSpacing(3)  # 输入框间距为3px

        channel_ratio_label = QLabel("渠道占比区间：")
        channel_ratio_label.setStyleSheet("font-size: 14px; font-weight: bold;")

        self.channel_ratio_min_input = QLineEdit()
        self.channel_ratio_min_input.setPlaceholderText("最小值%")
        self.channel_ratio_min_input.setFixedSize(85, 40)
        self.channel_ratio_min_input.setText(str(self.filter_settings["渠道占比最小值"]))

        channel_ratio_separator = QLabel("~")
        channel_ratio_separator.setAlignment(Qt.AlignCenter)
        channel_ratio_separator.setFixedWidth(20)
        channel_ratio_separator.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            border: none;
            background: transparent;
            color: #666666;
        """)

        self.channel_ratio_max_input = QLineEdit()
        self.channel_ratio_max_input.setPlaceholderText("最大值%")
        self.channel_ratio_max_input.setFixedSize(85, 40)
        self.channel_ratio_max_input.setText(str(self.filter_settings["渠道占比最大值"]))

        channel_ratio_layout.addWidget(channel_ratio_label)
        channel_ratio_layout.addWidget(self.channel_ratio_min_input)
        channel_ratio_layout.addWidget(channel_ratio_separator)
        channel_ratio_layout.addWidget(self.channel_ratio_max_input)

        # 第三部分：功能按钮区域
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        self.create_buttons(button_layout)

        # 添加到主布局 - 三个部分水平排列
        main_layout.addWidget(trade_index_widget)  # 成交指数区间
        main_layout.addWidget(channel_ratio_widget)  # 渠道占比区间
        main_layout.addWidget(button_widget)  # 功能按钮
        main_layout.addStretch()  # 右侧弹性空间

        # 连接输入框变化事件
        self.trade_index_min_input.textChanged.connect(self.on_filter_settings_changed)
        self.trade_index_max_input.textChanged.connect(self.on_filter_settings_changed)
        self.channel_ratio_min_input.textChanged.connect(self.on_filter_settings_changed)
        self.channel_ratio_max_input.textChanged.connect(self.on_filter_settings_changed)

        return data_filter_group

    def on_filter_settings_changed(self):
        """过滤设置变化处理"""
        try:
            # 获取当前输入值
            trade_index_min = int(self.trade_index_min_input.text() or "0")
            trade_index_max = int(self.trade_index_max_input.text() or "999999")
            channel_ratio_min = float(self.channel_ratio_min_input.text() or "0")
            channel_ratio_max = float(self.channel_ratio_max_input.text() or "100")

            # 更新设置
            self.filter_settings.update({
                "成交指数最小值": trade_index_min,
                "成交指数最大值": trade_index_max,
                "渠道占比最小值": channel_ratio_min,
                "渠道占比最大值": channel_ratio_max
            })

            # 保存设置
            self.save_filter_settings()

        except ValueError:
            # 输入值无效时不处理
            pass

    def save_filter_settings(self):
        """保存过滤设置到文件"""
        try:
            data_dir = Path("data")
            data_dir.mkdir(exist_ok=True)

            settings_file = data_dir / "Category data.txt"

            # 读取现有的类目数据
            existing_data = {}
            if settings_file.exists():
                try:
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                except:
                    existing_data = {}

            # 添加过滤设置
            existing_data["filter_settings"] = self.filter_settings

            # 保存到文件
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存过滤设置失败: {e}")

    def load_filter_settings(self):
        """从文件加载过滤设置"""
        try:
            data_dir = Path("data")
            settings_file = data_dir / "Category data.txt"

            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 加载过滤设置
                if "filter_settings" in data:
                    saved_settings = data["filter_settings"]
                    self.filter_settings.update(saved_settings)

                    # 更新界面输入框（如果已创建）
                    self.update_filter_settings_ui()

        except Exception as e:
            print(f"加载过滤设置失败: {e}")

    def update_filter_settings_ui(self):
        """更新过滤设置界面"""
        try:
            if hasattr(self, 'trade_index_min_input'):
                self.trade_index_min_input.setText(str(self.filter_settings["成交指数最小值"]))
            if hasattr(self, 'trade_index_max_input'):
                self.trade_index_max_input.setText(str(self.filter_settings["成交指数最大值"]))
            if hasattr(self, 'channel_ratio_min_input'):
                self.channel_ratio_min_input.setText(str(self.filter_settings["渠道占比最小值"]))
            if hasattr(self, 'channel_ratio_max_input'):
                self.channel_ratio_max_input.setText(str(self.filter_settings["渠道占比最大值"]))
        except Exception as e:
            print(f"更新过滤设置界面失败: {e}")

    def create_filters(self, layout):
        """创建筛选器"""
        self.filters = {}

        # 日期筛选器
        self.create_date_filter(layout, 0)

        # 类目筛选器
        category_labels = ["一级类目", "二级类目", "三级类目", "四级类目"]
        for i, label_text in enumerate(category_labels, 1):
            self.create_category_filter(layout, i, label_text)

        # 其他筛选器
        other_filters = [
            ("售卖渠道", ["全部", "直播间", "短视频", "商品卡"]),
            ("售卖形式", ["全部", "自卖", "分销"]),
            ("品牌商品", ["全部", "自卖", "分销"]),
            ("大牌大补", ["全部", "自卖", "分销"])
        ]

        for i, (label_text, options) in enumerate(other_filters, 5):
            self.create_simple_filter(layout, i, label_text, options)

    def create_date_filter(self, layout, col):
        """创建日期筛选器"""
        # 标签
        label = QLabel("日期")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label, 0, col)

        # 下拉框
        combo = QComboBox()
        combo.setMinimumHeight(30)
        combo.setMaxVisibleItems(10)  # 设置最大可见项目数


        # 生成52周的日期选项
        date_options = self.generate_week_options()

        # 一次性添加所有选项，避免逐个显示
        combo.blockSignals(True)  # 暂时阻止信号
        combo.addItems(date_options)
        combo.blockSignals(False)  # 恢复信号

        # 设置下拉框属性以确保立即显示所有选项
        combo.setInsertPolicy(QComboBox.NoInsert)
        combo.setSizeAdjustPolicy(QComboBox.AdjustToContents)

        # 为日期筛选器设置宽度为660px
        combo.setMaximumWidth(360)  # 设置最大宽度为660px
        combo.setMinimumWidth(360)  # 设置最小宽度确保一致性

        # 使用样式表确保宽度设置生效
        combo.setStyleSheet("""
            QComboBox {
                min-width: 240px;
                max-width: 240px;
                width: 240px;
            }
        """)

        layout.addWidget(combo, 1, col)
        self.filters["日期"] = combo

        # 优化下拉框显示性能
        self.optimize_combobox_performance(combo)

    def create_category_filter(self, layout, col, label_text):
        """创建类目筛选器"""
        # 标签
        label = QLabel(label_text)
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label, 0, col)

        # 下拉框
        combo = QComboBox()
        combo.setMinimumHeight(30)
        combo.setMaxVisibleItems(15)  # 类目较多，设置更多可见项

        # 设置下拉框属性以确保立即显示所有选项
        combo.setInsertPolicy(QComboBox.NoInsert)
        combo.setSizeAdjustPolicy(QComboBox.AdjustToContents)

        combo.blockSignals(True)  # 暂时阻止信号
        combo.addItem("")  # 默认空选项
        combo.blockSignals(False)  # 恢复信号

        layout.addWidget(combo, 1, col)

        self.filters[label_text] = combo

        # 优化下拉框显示性能
        self.optimize_combobox_performance(combo)

        # 强制设置选中项样式
        self.force_combobox_selection_style(combo)

        # 连接信号，实现级联选择
        if label_text == "一级类目":
            combo.currentTextChanged.connect(self.on_first_category_changed)
        elif label_text == "二级类目":
            combo.currentTextChanged.connect(self.on_second_category_changed)
        elif label_text == "三级类目":
            combo.currentTextChanged.connect(self.on_third_category_changed)

    def create_simple_filter(self, layout, col, label_text, options):
        """创建简单筛选器"""
        # 标签
        label = QLabel(label_text)
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label, 0, col)

        # 下拉框
        combo = QComboBox()
        combo.setMinimumHeight(30)
        combo.setMaxVisibleItems(8)  # 其他筛选器选项较少

        # 设置下拉框属性以确保立即显示所有选项
        combo.setInsertPolicy(QComboBox.NoInsert)
        combo.setSizeAdjustPolicy(QComboBox.AdjustToContents)

        # 一次性添加所有选项
        combo.blockSignals(True)  # 暂时阻止信号
        combo.addItems(options)
        combo.blockSignals(False)  # 恢复信号

        layout.addWidget(combo, 1, col)

        self.filters[label_text] = combo

        # 优化下拉框显示性能
        self.optimize_combobox_performance(combo)

        # 强制设置选中项样式
        self.force_combobox_selection_style(combo)

    def generate_week_options(self):
        """生成52周的日期选项"""
        options = []
        today = datetime.now()

        # 找到本周一
        days_since_monday = today.weekday()
        current_monday = today - timedelta(days=days_since_monday)

        for i in range(52):
            week_monday = current_monday - timedelta(weeks=i)
            week_sunday = week_monday + timedelta(days=6)

            week_str = f"{week_monday.strftime('%Y-%m-%d')}-{week_sunday.strftime('%Y-%m-%d')}"
            if i == 0:
                week_str = f"本周 ({week_str})"
            options.append(week_str)

        return options

    def optimize_combobox_performance(self, combo):
        """优化下拉框性能，确保选项立即全部显示"""
        # 关键：禁用下拉框的展开动画效果
        combo.setProperty("animated", False)

        # 设置视图属性
        view = combo.view()
        if view:
            # 禁用所有动画和过渡效果
            view.setProperty("animated", False)
            view.setProperty("showDropIndicator", False)

            # 设置统一的行高，避免动态计算
            view.setUniformItemSizes(True)

            # 禁用视图的动画效果
            view.setVerticalScrollMode(view.ScrollPerItem)
            view.setHorizontalScrollMode(view.ScrollPerItem)

            # 设置选择模式
            view.setSelectionMode(view.SingleSelection)

            # 强制立即更新视图
            view.setUpdatesEnabled(True)

            # 设置调色板来强制文字颜色
            palette = view.palette()
            palette.setColor(QPalette.HighlightedText, QColor("#1976d2"))  # 选中文字颜色
            palette.setColor(QPalette.Highlight, QColor("#e3f2fd"))        # 选中背景颜色
            palette.setColor(QPalette.Text, QColor("#333333"))             # 普通文字颜色
            view.setPalette(palette)

            # 设置文本省略号模式为结尾省略
            if hasattr(view, 'setTextElideMode'):
                view.setTextElideMode(Qt.ElideRight)  # 在右侧（结尾）显示省略号

        # 设置下拉框属性
        combo.setMaxCount(1000)
        combo.setInsertPolicy(QComboBox.NoInsert)

        # 禁用下拉框的自动调整大小动画
        combo.setSizeAdjustPolicy(QComboBox.AdjustToMinimumContentsLengthWithIcon)

    def force_combobox_selection_style(self, combo):
        """强制设置下拉框选中项样式"""
        try:
            # 设置强制样式
            combo.setStyleSheet(combo.styleSheet() + """
                QComboBox QAbstractItemView::item {
                    text-overflow: ellipsis !important;
                    overflow: hidden !important;
                    white-space: nowrap !important;
                }
                QComboBox QAbstractItemView::item:selected {
                    background-color: #e3f2fd !important;
                    color: #1976d2 !important;
                }
                QComboBox QAbstractItemView::item:hover {
                    background-color: #f5f5f5 !important;
                    color: #333333 !important;
                }
            """)

            # 获取视图并设置调色板和省略号模式
            view = combo.view()
            if view:
                palette = view.palette()
                palette.setColor(QPalette.HighlightedText, QColor("#1976d2"))
                palette.setColor(QPalette.Highlight, QColor("#e3f2fd"))
                palette.setColor(QPalette.Text, QColor("#333333"))
                view.setPalette(palette)

                # 设置文本省略号模式为结尾省略
                if hasattr(view, 'setTextElideMode'):
                    view.setTextElideMode(Qt.ElideRight)  # 在右侧（结尾）显示省略号

        except Exception as e:
            print(f"设置下拉框选中样式失败: {e}")

    def create_buttons(self, layout):
        """创建功能按钮"""
        button_configs = [
            ("登录", "login_btn", "#4CAF50"),
            ("开始采集", "start_btn", "#2196F3"),
            ("停止采集", "stop_btn", "#f44336"),
            ("导出数据", "export_btn", "#FF9800"),
            ("解析类目", "parse_category_btn", "#9C27B0")
        ]
        
        layout.addStretch()
        
        for text, attr_name, color in button_configs:
            btn = QPushButton(text)
            btn.setMinimumHeight(35)
            btn.setMinimumWidth(80)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-weight: bold;
                    padding: 8px 16px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
                QPushButton:disabled {{
                    background-color: #cccccc;
                    color: #666666;
                }}
            """)
            
            setattr(self, attr_name, btn)
            layout.addWidget(btn)

        layout.addStretch()

        # 连接按钮事件
        self.login_btn.clicked.connect(self.on_login_clicked)
        self.start_btn.clicked.connect(self.on_start_clicked)
        self.stop_btn.clicked.connect(self.on_stop_clicked)
        self.export_btn.clicked.connect(self.on_export_clicked)
        self.parse_category_btn.clicked.connect(self.on_parse_category_clicked)
        
    def create_data_widget(self):
        """创建数据展示区域"""
        data_frame = QFrame()
        data_frame.setFrameStyle(QFrame.Box)
        data_frame.setLineWidth(1)
        
        layout = QVBoxLayout(data_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 状态显示区域
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Box)
        status_frame.setLineWidth(1)
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_frame)
        
        # 数据表格
        self.create_data_table()
        layout.addWidget(self.data_table)
        
        return data_frame
        
    def create_data_table(self):
        """创建数据表格"""
        self.data_table = QTableWidget()
        
        # 设置表头 - 按照新的字段排列顺序
        headers = ["排名", "标题", "链接", "成交指数",
                  "总成交指数", "渠道占比", "支付件数", "成交商家数", "类目"]
        
        self.data_table.setColumnCount(len(headers))
        self.data_table.setHorizontalHeaderLabels(headers)
        
        # 设置表格样式
        self.data_table.horizontalHeader().setStretchLastSection(False)  # 关闭最后一列自动拉伸
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QTableWidget.SelectRows)

        # 隐藏默认的行号，因为我们有自定义的排名列
        self.data_table.verticalHeader().setVisible(False)
        
        # 设置列宽
        self.apply_column_widths()

        # 不添加示例数据，等待采集功能开发

    def update_data_table(self, data: list):
        """更新数据表格"""
        try:
            current_row_count = self.data_table.rowCount()

            for item in data:
                row = current_row_count
                self.data_table.insertRow(row)

                # 按列顺序填充数据 - 按照新的字段排列顺序
                columns = ["排名", "标题", "链接", "成交指数",
                          "总成交指数", "渠道占比", "支付件数", "成交商家数", "类目"]

                for col, column_name in enumerate(columns):
                    value = item.get(column_name, "")

                    # 创建表格项
                    table_item = QTableWidgetItem(str(value))

                    # 设置居中对齐
                    table_item.setTextAlignment(Qt.AlignCenter)

                    # 如果是链接列，设置为可点击
                    if column_name == "链接" and value:
                        table_item.setToolTip("点击打开链接")

                    self.data_table.setItem(row, col, table_item)

                current_row_count += 1

            # 重新应用列宽设置（保持链接列固定宽度）
            self.apply_column_widths()

        except Exception as e:
            print(f"更新数据表格失败: {e}")

    def apply_column_widths(self):
        """应用列宽设置"""
        try:
            headers = ["排名", "标题", "链接", "成交指数",
                      "总成交指数", "渠道占比", "支付件数", "成交商家数", "类目"]

            header = self.data_table.horizontalHeader()
            for i in range(len(headers)):
                if i == 0:  # 排名列
                    header.setSectionResizeMode(i, QHeaderView.Fixed)
                    self.data_table.setColumnWidth(i, 50)  # 排名列固定50px
                elif i == 1:  # 标题列
                    header.setSectionResizeMode(i, QHeaderView.Stretch)  # 标题列自适应
                elif i == 2:  # 链接列
                    header.setSectionResizeMode(i, QHeaderView.Stretch)  # 链接列自适应
                elif i == 8:  # 类目列（第9列，索引为8）
                    header.setSectionResizeMode(i, QHeaderView.Fixed)
                    self.data_table.setColumnWidth(i, 400)  # 类目列固定400px
                else:  # 其他列（成交指数、总成交指数、渠道占比、支付件数、成交商家数）
                    header.setSectionResizeMode(i, QHeaderView.Fixed)
                    self.data_table.setColumnWidth(i, 100)  # 其他列固定100px
        except Exception as e:
            print(f"应用列宽设置失败: {e}")

    def clear_data_table(self):
        """清空数据表格"""
        self.data_table.setRowCount(0)
        self.collected_data.clear()
        self.current_row_count = 0  # 重置行计数器
                
    def setup_styles(self):
        """设置样式"""
        # 设置整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: white;
                border-radius: 4px;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #cccccc;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 8px 16px;
                margin-right: 2px;
                color: #495057;
                font-weight: bold;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background-color: white;
                color: #2c3e50;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background-color: #e9ecef;
            }
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 15px;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 13px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 13px;
                padding: 0 6px 0 6px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QComboBox {
                border: 1px solid #cccccc;
                border-radius: 3px;
                padding: 5px;
                background-color: white;
                /* min-width由各个筛选器单独设置 */
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #cccccc;
                background-color: white;
                selection-background-color: #e3f2fd;
                outline: none;
                max-height: 300px;
            }
            QComboBox QAbstractItemView::item {
                height: 25px;
                padding: 2px 8px;
                border: none;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #e3f2fd !important;
                color: #1976d2 !important;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #f5f5f5 !important;
                color: #333333 !important;
            }
            QComboBox QAbstractItemView::item:focus {
                background-color: #e3f2fd !important;
                color: #1976d2 !important;
            }
            QLineEdit {
                border: 1px solid #cccccc;
                border-radius: 5px;
                padding: 6px;
                background-color: white;
                font-size: 15px;
                box-sizing: border-box;
            }
            QLineEdit:focus {
                border: 2px solid #2196F3;
                background-color: #f8f9fa;
            }
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
    def darken_color(self, color):
        """使颜色变暗"""
        color_map = {
            "#4CAF50": "#45a049",
            "#2196F3": "#1976D2",
            "#f44336": "#d32f2f",
            "#FF9800": "#F57C00",
            "#9C27B0": "#7B1FA2"
        }
        return color_map.get(color, color)
        
    def check_cookies(self):
        """检查Cookie状态"""
        # 检查data目录下是否有cookie文件
        data_dir = Path("data")
        cookie_files = ["cookies.txt", "cookies2.txt"]
        
        has_cookies = data_dir.exists() and all(
            (data_dir / file).exists() for file in cookie_files
        )
        
        # 根据Cookie状态设置按钮状态
        # 解析类目按钮始终可用（不依赖Cookie）
        self.parse_category_btn.setEnabled(True)

        # 开始按钮需要Cookie和类目数据
        can_start = has_cookies and bool(self.category_data)
        self.start_btn.setEnabled(can_start)

        # 停止按钮在采集时启用
        self.stop_btn.setEnabled(False)

        # 导出按钮需要有采集数据
        self.export_btn.setEnabled(bool(self.collected_data))
            
        if has_cookies:
            self.status_label.setText("已检测到Cookie，可以开始使用")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)
        else:
            self.status_label.setText("未检测到Cookie，请先登录")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #f8d7da;
                    color: #721c24;
                    border: 1px solid #f5c6cb;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)

    def load_category_data(self):
        """加载类目数据"""
        data_dir = Path("data")
        category_file = data_dir / "Category data.txt"

        if category_file.exists():
            try:
                with open(category_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 如果数据包含过滤设置，分离类目数据和过滤设置
                if "filter_settings" in data:
                    self.category_data = {k: v for k, v in data.items() if k != "filter_settings"}
                else:
                    self.category_data = data

                self.update_category_filters()
                # 重新检查按钮状态
                if hasattr(self, 'start_btn'):
                    self.check_cookies()
            except Exception as e:
                print(f"加载类目数据失败: {e}")
                self.category_data = {}
        else:
            self.category_data = {}

    def update_category_filters(self):
        """更新类目筛选器选项"""
        if not self.category_data:
            return

        # 更新一级类目
        first_combo = self.filters.get("一级类目")
        if first_combo:
            first_combo.blockSignals(True)  # 暂时阻止信号
            first_combo.clear()
            first_combo.addItem("")

            # 一次性添加所有选项
            category_names = list(self.category_data.keys())
            first_combo.addItems(category_names)
            first_combo.blockSignals(False)  # 恢复信号

    def on_first_category_changed(self, text):
        """一级类目改变时的处理"""
        # 清空下级类目
        for level in ["二级类目", "三级类目", "四级类目"]:
            combo = self.filters.get(level)
            if combo:
                combo.blockSignals(True)
                combo.clear()
                combo.addItem("")
                combo.blockSignals(False)

        if not text or text not in self.category_data:
            return

        # 更新二级类目
        second_combo = self.filters.get("二级类目")
        if second_combo and text in self.category_data:
            children = self.category_data[text].get('children', {})
            category_names = list(children.keys())

            second_combo.blockSignals(True)
            second_combo.addItems(category_names)
            second_combo.blockSignals(False)

    def on_second_category_changed(self, text):
        """二级类目改变时的处理"""
        # 清空下级类目
        for level in ["三级类目", "四级类目"]:
            combo = self.filters.get(level)
            if combo:
                combo.blockSignals(True)
                combo.clear()
                combo.addItem("")
                combo.blockSignals(False)

        if not text:
            return

        # 获取当前一级类目
        first_text = self.filters["一级类目"].currentText()
        if not first_text or first_text not in self.category_data:
            return

        # 更新三级类目
        third_combo = self.filters.get("三级类目")
        first_children = self.category_data[first_text].get('children', {})
        if third_combo and text in first_children:
            second_children = first_children[text].get('children', {})
            category_names = list(second_children.keys())

            third_combo.blockSignals(True)
            third_combo.addItems(category_names)
            third_combo.blockSignals(False)

    def on_third_category_changed(self, text):
        """三级类目改变时的处理"""
        # 清空四级类目
        fourth_combo = self.filters.get("四级类目")
        if fourth_combo:
            fourth_combo.blockSignals(True)
            fourth_combo.clear()
            fourth_combo.addItem("")
            fourth_combo.blockSignals(False)

        if not text:
            return

        # 获取当前一、二级类目
        first_text = self.filters["一级类目"].currentText()
        second_text = self.filters["二级类目"].currentText()

        if not first_text or not second_text:
            return

        # 更新四级类目
        try:
            first_children = self.category_data[first_text].get('children', {})
            second_children = first_children[second_text].get('children', {})
            if text in second_children:
                third_children = second_children[text].get('children', [])
                category_names = [item['name'] for item in third_children]

                fourth_combo.blockSignals(True)
                fourth_combo.addItems(category_names)
                fourth_combo.blockSignals(False)
        except KeyError:
            pass  # 如果路径不存在，忽略错误

    def on_login_clicked(self):
        """登录按钮点击事件"""
        try:
            # 更新状态
            self.status_label.setText("正在启动登录程序...")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #d1ecf1;
                    color: #0c5460;
                    border: 1px solid #bee5eb;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)

            # 启动Cookie导出程序
            import subprocess
            import sys

            # 使用subprocess启动cookie_exporter.py
            subprocess.Popen([sys.executable, "cookie_exporter.py"])

            # 启动定时器定期检查Cookie状态
            self.cookie_check_timer = QTimer()
            self.cookie_check_timer.timeout.connect(self.check_cookies)
            self.cookie_check_timer.start(1000)  # 每秒检查一次

        except Exception as e:
            self.status_label.setText("启动登录程序失败")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #f8d7da;
                    color: #721c24;
                    border: 1px solid #f5c6cb;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)
            QMessageBox.critical(self, "错误", f"启动登录程序失败：{str(e)}")

    def check_cookies_after_login(self):
        """登录后检查Cookie状态"""
        self.check_cookies()

        # 如果检测到Cookie，停止定时器
        data_dir = Path("data")
        cookie_files = ["cookies.txt", "cookies2.txt"]

        has_cookies = data_dir.exists() and all(
            (data_dir / file).exists() for file in cookie_files
        )

        if has_cookies and hasattr(self, 'cookie_check_timer'):
            self.cookie_check_timer.stop()  # 停止定时检查
        elif not has_cookies:
            self.status_label.setText("请在登录窗口中完成登录操作")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #fff3cd;
                    color: #856404;
                    border: 1px solid #ffeaa7;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)

    def on_start_clicked(self):
        """开始按钮点击事件"""
        try:
            # 检查必要条件
            if not self.category_data:
                QMessageBox.warning(self, "警告", "请先解析类目数据")
                return

            # 获取筛选条件
            filters = self.get_current_filters()
            if not self.validate_filters(filters):
                return

            # 验证过滤设置
            if not self.validate_filter_settings():
                return

            # 设置采集器筛选条件和过滤设置
            self.data_collector.set_filters(filters)
            self.data_collector.set_filter_settings(self.filter_settings)

            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.export_btn.setEnabled(False)

            # 清空之前的数据
            self.collected_data.clear()
            self.data_table.setRowCount(0)
            self.current_row_count = 0  # 重置行计数器

            # 开始采集
            self.data_collector.start_collection()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动采集失败：{str(e)}")

    def validate_filter_settings(self) -> bool:
        """验证过滤设置"""
        try:
            # 验证成交指数设置
            trade_min = int(self.trade_index_min_input.text() or "0")
            trade_max = int(self.trade_index_max_input.text() or "999999")

            if trade_min < 0:
                QMessageBox.warning(self, "警告", "成交指数最小值不能小于0")
                return False

            if trade_max <= trade_min:
                QMessageBox.warning(self, "警告", "成交指数最大值必须大于最小值")
                return False

            # 验证渠道占比设置
            ratio_min = float(self.channel_ratio_min_input.text() or "0")
            ratio_max = float(self.channel_ratio_max_input.text() or "100")

            if ratio_min < 0 or ratio_min > 100:
                QMessageBox.warning(self, "警告", "渠道占比最小值必须在0-100之间")
                return False

            if ratio_max < 0 or ratio_max > 200:
                QMessageBox.warning(self, "警告", "渠道占比最大值必须在0-200之间")
                return False

            if ratio_max <= ratio_min:
                QMessageBox.warning(self, "警告", "渠道占比最大值必须大于最小值")
                return False

            return True

        except ValueError:
            QMessageBox.warning(self, "警告", "请输入有效的数值")
            return False

    def on_stop_clicked(self):
        """停止按钮点击事件"""
        try:
            if self.data_collector and self.data_collector.is_running:
                self.data_collector.stop_collection()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止采集失败：{str(e)}")

    def on_export_clicked(self):
        """导出按钮点击事件"""
        if not self.collected_data:
            QMessageBox.warning(self, "警告", "没有可导出的数据，请先进行数据采集")
            return

        try:
            from datetime import datetime
            import pandas as pd

            # 创建DataFrame
            df = pd.DataFrame(self.collected_data)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"快手采集数据_{timestamp}.xlsx"

            # 导出到Excel
            df.to_excel(filename, index=False, engine='openpyxl')

            QMessageBox.information(self, "成功", f"数据已导出到：{filename}")

        except ImportError:
            QMessageBox.critical(self, "错误", "缺少pandas或openpyxl库，请安装：pip install pandas openpyxl")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")

    def init_data_collector(self):
        """初始化数据采集器"""
        try:
            from data_collector import DataCollector

            self.data_collector = DataCollector()

            # 连接信号
            self.data_collector.progress_updated.connect(self.on_progress_updated)
            self.data_collector.data_received.connect(self.on_data_received)
            self.data_collector.single_item_ready.connect(self.on_single_item_ready)  # 新增：逐行显示信号
            self.data_collector.error_occurred.connect(self.on_error_occurred)
            self.data_collector.collection_finished.connect(self.on_collection_finished)
            self.data_collector.status_changed.connect(self.on_status_changed)

        except Exception as e:
            print(f"初始化数据采集器失败: {e}")

    def get_current_filters(self) -> dict:
        """获取当前筛选条件"""
        filters = {}

        for filter_name, combo in self.filters.items():
            filters[filter_name] = combo.currentText()

        return filters

    def validate_filters(self, filters: dict) -> bool:
        """验证筛选条件"""
        # 检查必要的筛选条件
        required_filters = ["日期", "售卖渠道", "售卖形式", "品牌商品", "大牌大补"]

        for filter_name in required_filters:
            if not filters.get(filter_name):
                QMessageBox.warning(self, "警告", f"请选择{filter_name}")
                return False

        # 检查日期格式
        date_str = filters.get("日期", "")
        if not date_str or date_str == "":
            QMessageBox.warning(self, "警告", "请选择日期范围")
            return False

        return True

    def on_progress_updated(self, message: str):
        """进度更新处理"""
        self.status_label.setText(message)

    def on_data_received(self, data: list):
        """数据接收处理（批量）"""
        # 按排名排序确保顺序正确
        sorted_data = self.sort_data_by_ranking(data)
        self.collected_data.extend(sorted_data)
        self.update_data_table(sorted_data)

    def on_single_item_ready(self, item: dict):
        """单个商品数据就绪处理（逐行显示）"""
        try:
            # 应用渠道占比过滤
            if not self.should_display_item(item):
                # 不符合渠道占比条件的商品不显示，但仍然添加到collected_data中
                self.collected_data.append(item)
                return

            # 修正排名确保连续性
            self.current_row_count += 1
            item["排名"] = self.current_row_count

            # 添加到收集的数据中
            self.collected_data.append(item)

            # 在表格中添加新行
            current_row_count = self.data_table.rowCount()
            self.data_table.insertRow(current_row_count)

            # 按列顺序填充数据
            columns = ["排名", "标题", "链接", "成交指数",
                      "总成交指数", "渠道占比", "支付件数", "成交商家数", "类目"]

            for col, column_name in enumerate(columns):
                value = item.get(column_name, "")

                # 创建表格项
                table_item = QTableWidgetItem(str(value))
                table_item.setTextAlignment(Qt.AlignCenter)

                # 设置链接列为可点击
                if column_name == "链接" and value:
                    table_item.setToolTip("点击打开链接")

                self.data_table.setItem(current_row_count, col, table_item)

            # 自动滚动到最新添加的行
            self.data_table.scrollToBottom()

        except Exception as e:
            print(f"添加单行数据失败: {e}")

    def should_display_item(self, item: dict) -> bool:
        """判断商品是否应该显示（渠道占比过滤）"""
        try:
            # 获取渠道占比
            channel_ratio_str = item.get("渠道占比", "0.00%")
            if not channel_ratio_str or channel_ratio_str == "":
                return True  # 如果没有渠道占比数据，默认显示

            # 解析渠道占比百分比
            channel_ratio = float(channel_ratio_str.replace("%", ""))

            # 检查是否在设定范围内
            min_ratio = self.filter_settings["渠道占比最小值"]
            max_ratio = self.filter_settings["渠道占比最大值"]

            return min_ratio <= channel_ratio <= max_ratio

        except (ValueError, TypeError):
            # 解析失败时默认显示
            return True

    def sort_data_by_ranking(self, data: list) -> list:
        """按排名对数据进行排序，确保显示顺序正确"""
        try:
            def get_rank_value(item):
                """获取排名值用于排序"""
                rank = item.get("排名", "")
                try:
                    # 尝试转换为整数进行排序
                    return int(rank) if rank else 999999
                except (ValueError, TypeError):
                    # 如果转换失败，放到最后
                    return 999999

            # 按排名排序
            sorted_data = sorted(data, key=get_rank_value)

            # 修正排名确保连续性
            for index, item in enumerate(sorted_data):
                expected_rank = index + 1
                actual_rank = item.get("排名", "")

                # 如果排名不连续，修正为正确的排名
                if str(actual_rank) != str(expected_rank):
                    item["排名"] = expected_rank

            return sorted_data

        except Exception as e:
            print(f"排序数据失败: {e}")
            return data  # 排序失败时返回原数据

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            print("正在关闭程序...")

            # 清理数据采集器资源
            if self.data_collector:
                print("清理数据采集器资源...")
                self.data_collector.cleanup()
                self.data_collector.deleteLater()
                self.data_collector = None

            # 清理商品查询工作线程
            if self.product_query_worker:
                print("清理商品查询工作线程...")
                if self.product_query_worker.is_running:
                    self.product_query_worker.stop_query()
                    self.product_query_worker.wait(3000)  # 等待最多3秒
                self.product_query_worker.deleteLater()
                self.product_query_worker = None

            print("程序关闭完成")
            event.accept()  # 接受关闭事件

            # 强制退出程序
            import os
            os._exit(0)  # 强制退出，不等待任何线程

        except Exception as e:
            print(f"关闭程序时出错: {e}")
            event.accept()  # 即使出错也要关闭
            import os
            os._exit(1)  # 强制退出

    def on_error_occurred(self, error: str):
        """错误处理"""
        self.status_label.setText(f"错误：{error}")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
        """)
        QMessageBox.critical(self, "采集错误", error)

    def on_collection_finished(self):
        """采集完成处理"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.export_btn.setEnabled(bool(self.collected_data))

        if self.collected_data:
            self.status_label.setText(f"采集完成，共获取 {len(self.collected_data)} 条数据")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)
        else:
            self.status_label.setText("采集完成，但未获取到数据")

    def on_status_changed(self, status: str):
        """状态变化处理"""
        if status == "就绪":
            self.check_cookies()  # 重新检查状态

    def on_parse_category_clicked(self):
        """解析类目按钮点击事件"""
        try:
            # 检查类目响应数据文件是否存在
            source_file = Path("类目响应数据.md")
            if not source_file.exists():
                QMessageBox.warning(self, "警告", "未找到'类目响应数据.md'文件，请确保文件存在于根目录")
                return

            # 更新状态
            self.status_label.setText("正在解析类目数据...")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #d1ecf1;
                    color: #0c5460;
                    border: 1px solid #bee5eb;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)

            # 导入并执行解析
            from category_parser import parse_categories

            if parse_categories():
                # 重新加载类目数据
                self.load_category_data()

                # 重新检查按钮状态
                self.check_cookies()

                self.status_label.setText("类目解析完成")
                self.status_label.setStyleSheet("""
                    QLabel {
                        background-color: #d4edda;
                        color: #155724;
                        border: 1px solid #c3e6cb;
                        border-radius: 4px;
                        padding: 8px;
                        font-weight: bold;
                    }
                """)

                QMessageBox.information(self, "成功", "类目数据解析完成！\n数据已保存到 data/Category data.txt")
            else:
                self.status_label.setText("类目解析失败")
                self.status_label.setStyleSheet("""
                    QLabel {
                        background-color: #f8d7da;
                        color: #721c24;
                        border: 1px solid #f5c6cb;
                        border-radius: 4px;
                        padding: 8px;
                        font-weight: bold;
                    }
                """)
                QMessageBox.critical(self, "错误", "类目数据解析失败，请检查文件格式")

        except Exception as e:
            self.status_label.setText("类目解析出错")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #f8d7da;
                    color: #721c24;
                    border: 1px solid #f5c6cb;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)
            QMessageBox.critical(self, "错误", f"解析类目失败：{str(e)}")

    def init_product_query(self):
        """初始化商品查询功能"""
        try:
            from product_query import ProductQueryWorker

            self.product_query_worker = ProductQueryWorker()

            # 连接信号
            self.product_query_worker.progress_updated.connect(self.on_query_progress_updated)
            self.product_query_worker.title_received.connect(self.on_query_title_received)
            self.product_query_worker.sales_data_received.connect(self.on_query_sales_data_received)
            self.product_query_worker.error_occurred.connect(self.on_query_error_occurred)
            self.product_query_worker.query_finished.connect(self.on_query_finished)

        except Exception as e:
            print(f"初始化商品查询功能失败: {e}")

    def on_start_query_clicked(self):
        """开始查询按钮点击事件"""
        try:
            # 检查是否有商品链接
            if self.query_data_table.rowCount() == 0:
                QMessageBox.warning(self, "警告", "请先导入商品链接")
                return

            # 获取商品链接
            product_links = []
            for row in range(self.query_data_table.rowCount()):
                link_item = self.query_data_table.item(row, 1)
                if link_item and link_item.text().strip():
                    product_links.append(link_item.text().strip())

            if not product_links:
                QMessageBox.warning(self, "警告", "没有有效的商品链接")
                return

            # 获取时间范围
            time_range_text = self.query_time_combo.currentText()
            days = int(time_range_text.replace("天", ""))

            # 获取查询速度模式
            speed_text = self.query_speed_combo.currentText()
            speed_mode_map = {"超快": "ultra_fast", "快速": "fast", "普通": "normal", "慢速": "slow"}
            speed_mode = speed_mode_map.get(speed_text, "fast")

            # 设置查询参数
            self.product_query_worker.set_query_params(product_links, days)
            self.product_query_worker.set_speed_mode(speed_mode)

            # 更新UI状态
            self.start_query_btn.setEnabled(False)
            self.stop_query_btn.setEnabled(True)
            self.export_query_btn.setEnabled(False)
            self.import_links_btn.setEnabled(False)

            # 清空现有数据（保留链接）
            for row in range(self.query_data_table.rowCount()):
                # 清空标题列
                title_item = self.query_data_table.item(row, 0)
                if title_item:
                    title_item.setText("")

                # 清空日期列
                for col in range(2, self.query_data_table.columnCount()):
                    date_item = self.query_data_table.item(row, col)
                    if date_item:
                        date_item.setText("")

            # 开始查询
            self.product_query_worker.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动查询失败：{str(e)}")

    def on_stop_query_clicked(self):
        """停止查询按钮点击事件"""
        try:
            if self.product_query_worker and self.product_query_worker.is_running:
                self.product_query_worker.stop_query()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止查询失败：{str(e)}")

    def on_export_query_clicked(self):
        """导出查询数据按钮点击事件"""
        try:
            if self.query_data_table.rowCount() == 0:
                QMessageBox.warning(self, "警告", "没有可导出的数据")
                return

            # 收集表格数据
            data = []
            headers = []

            # 获取表头
            for col in range(self.query_data_table.columnCount()):
                header_item = self.query_data_table.horizontalHeaderItem(col)
                headers.append(header_item.text() if header_item else f"列{col+1}")

            # 获取数据
            for row in range(self.query_data_table.rowCount()):
                row_data = {}
                for col in range(self.query_data_table.columnCount()):
                    item = self.query_data_table.item(row, col)
                    row_data[headers[col]] = item.text() if item else ""
                data.append(row_data)

            # 创建DataFrame
            df = pd.DataFrame(data)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"商品成交量查询_{timestamp}.xlsx"

            # 导出到Excel
            df.to_excel(filename, index=False, engine='openpyxl')

            QMessageBox.information(self, "成功", f"数据已导出到：{filename}")

        except ImportError:
            QMessageBox.critical(self, "错误", "缺少pandas或openpyxl库，请安装：pip install pandas openpyxl")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")

    def on_query_progress_updated(self, message: str):
        """查询进度更新处理"""
        self.query_status_label.setText(message)

    def on_query_title_received(self, row: int, title: str, link: str):
        """商品标题接收处理"""
        try:
            if row < self.query_data_table.rowCount():
                # 更新标题列
                title_item = self.query_data_table.item(row, 0)
                if title_item:
                    # 限制标题长度为50字符
                    display_title = title[:50] + "..." if len(title) > 50 else title
                    title_item.setText(display_title)
                    title_item.setToolTip(title)  # 鼠标悬停显示完整标题

        except Exception as e:
            print(f"更新商品标题失败: {e}")

    def on_query_sales_data_received(self, row: int, date_index: int, sales_count: int):
        """成交量数据接收处理"""
        try:
            if row < self.query_data_table.rowCount():
                col = date_index + 2  # 日期列从第3列开始
                if col < self.query_data_table.columnCount():
                    date_item = self.query_data_table.item(row, col)
                    if date_item:
                        date_item.setText(str(sales_count))

        except Exception as e:
            print(f"更新成交量数据失败: {e}")

    def on_query_error_occurred(self, error: str):
        """查询错误处理"""
        self.query_status_label.setText(f"错误：{error}")
        self.query_status_label.setStyleSheet("""
            QLabel {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
        """)
        QMessageBox.critical(self, "查询错误", error)

    def on_query_finished(self):
        """查询完成处理"""
        self.start_query_btn.setEnabled(True)
        self.stop_query_btn.setEnabled(False)
        self.export_query_btn.setEnabled(True)
        self.import_links_btn.setEnabled(True)

        self.query_status_label.setText("查询完成")
        self.query_status_label.setStyleSheet("""
            QLabel {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
        """)

    # 采集相关方法已删除（采集功能未开发）


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("快手采集工具")
    app.setApplicationVersion("1.0")

    # 禁用所有动画效果
    app.setEffectEnabled(Qt.UI_AnimateMenu, False)
    app.setEffectEnabled(Qt.UI_AnimateCombo, False)
    app.setEffectEnabled(Qt.UI_AnimateTooltip, False)
    app.setEffectEnabled(Qt.UI_FadeMenu, False)
    app.setEffectEnabled(Qt.UI_FadeTooltip, False)

    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)

    # 创建主窗口
    window = KuaishouCollectorMainWindow()
    window.show()

    try:
        # 运行应用程序
        exit_code = app.exec_() if PYQT_VERSION == 5 else app.exec()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序运行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
